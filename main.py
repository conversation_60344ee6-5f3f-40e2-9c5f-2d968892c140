"""
提现充值服务API客户端
用于查询指定时间范围内的提现充值记录
"""

import requests
import json
from datetime import datetime
from urllib.parse import quote
from typing import Dict, Optional, Tuple


class WithdrawDepositClient:
    """提现充值服务API客户端"""

    def __init__(self, base_url: str, token: str):
        """
        初始化客户端

        Args:
            base_url (str): API基础URL
            token (str): 认证令牌
        """
        self.base_url = base_url
        self.token = token
        self.session = requests.Session()
        self._setup_headers()

    def _setup_headers(self) -> None:
        """设置请求头"""
        self.session.headers.update({
            'Host': '************:44371',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'Accept': 'application/json, text/plain, */*',
            'authorization': f'Bearer {self.token}',
            'Origin': 'http://************:8001',
            'Referer': 'http://************:8001/',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
        })

    def get_today_time_range(self) -> Tuple[str, str]:
        """
        获取当天的时间范围（00:00:00 到 23:59:59）

        Returns:
            Tuple[str, str]: (开始时间, 结束时间)
        """
        today = datetime.now()
        start_time = today.replace(hour=0, minute=0, second=0, microsecond=0)
        end_time = today.replace(hour=23, minute=59, second=59, microsecond=0)

        return (
            start_time.strftime('%Y/%m/%d %H:%M:%S'),
            end_time.strftime('%Y/%m/%d %H:%M:%S')
        )

    def build_query_url(self, skip_count: int = 0, max_result_count: int = 500,
                       start_time: Optional[str] = None, end_time: Optional[str] = None) -> str:
        """
        构建查询URL

        Args:
            skip_count (int): 跳过记录数
            max_result_count (int): 最大返回记录数
            start_time (Optional[str]): 开始时间，格式：YYYY/MM/DD HH:MM:SS
            end_time (Optional[str]): 结束时间，格式：YYYY/MM/DD HH:MM:SS

        Returns:
            str: 完整的查询URL
        """
        # 如果没有指定时间，使用当天时间范围
        if start_time is None or end_time is None:
            start_time, end_time = self.get_today_time_range()

        # URL编码时间参数
        start_time_encoded = quote(start_time)
        end_time_encoded = quote(end_time)

        return (f'{self.base_url}/api/vnd/withdraw-deposit-app-servicecs'
                f'?skipCount={skip_count}'
                f'&maxResultCount={max_result_count}'
                f'&startTime={start_time_encoded}'
                f'&endTime={end_time_encoded}')

    def query_records(self, skip_count: int = 0, max_result_count: int = 500,
                     start_time: Optional[str] = None, end_time: Optional[str] = None) -> Dict:
        """
        查询提现充值记录

        Args:
            skip_count (int): 跳过记录数
            max_result_count (int): 最大返回记录数
            start_time (Optional[str]): 开始时间
            end_time (Optional[str]): 结束时间

        Returns:
            Dict: API响应数据

        Raises:
            requests.RequestException: 请求失败时抛出异常
        """
        url = self.build_query_url(skip_count, max_result_count, start_time, end_time)

        try:
            response = self.session.get(url)
            response.raise_for_status()  # 检查HTTP状态码
            return response.json()
        except requests.RequestException as e:
            print(f"请求失败: {e}")
            raise
        except json.JSONDecodeError as e:
            print(f"JSON解析失败: {e}")
            print(f"响应内容: {response.text}")
            raise


def main():
    """主函数"""
    # 配置参数
    BASE_URL = 'http://************:44371'
    TOKEN = '*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'

    # 创建客户端实例
    client = WithdrawDepositClient(BASE_URL, TOKEN)

    try:
        # 查询当天的记录
        print("正在查询当天的提现充值记录...")
        data = client.query_records()

        # 格式化输出
        print(json.dumps(data, indent=2, ensure_ascii=False))

    except Exception as e:
        print(f"程序执行失败: {e}")


if __name__ == "__main__":
    main()