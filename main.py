import requests

headers = {
    'Host': '************:44371',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    'Accept': 'application/json, text/plain, */*',
    # 'Accept-Encoding': 'gzip, deflate',
    'authorization': 'Bearer *********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
    'Origin': 'http://************:8001',
    'Referer': 'http://************:8001/',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
}

response = requests.get(
    'http://************:44371/api/vnd/withdraw-deposit-app-servicecs?skipCount=0&maxResultCount=20&startTime=2025-07-01+00:00:00&endTime=2025-07-31+23:59:59',
    headers=headers,
)
print(response.text)