import requests
import time
from datetime import datetime
from urllib.parse import quote



cookie = '*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'

headers = {
    'Host': '************:44371',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    'Accept': 'application/json, text/plain, */*',
    'authorization': f'Bearer {cookie}',
    'Origin': 'http://************:8001',
    'Referer': 'http://************:8001/',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
}

# 获取当天的开始和结束时间
today = datetime.now()
start_time = today.replace(hour=0, minute=0, second=0, microsecond=0).strftime('%Y/%m/%d %H:%M:%S')
end_time = today.replace(hour=23, minute=59, second=59, microsecond=0).strftime('%Y/%m/%d %H:%M:%S')

# URL编码时间参数
start_time_encoded = quote(start_time)
end_time_encoded = quote(end_time)

# 构建URL
url = f'http://************:44371/api/vnd/withdraw-deposit-app-servicecs?skipCount=0&maxResultCount=20&startTime={start_time_encoded}&endTime={end_time_encoded}'

response = requests.get(url, headers=headers)
# print(response.text)